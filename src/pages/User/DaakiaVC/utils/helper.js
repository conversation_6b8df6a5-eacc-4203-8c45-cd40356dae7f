import { Track } from "livekit-client";
import CryptoJ<PERSON> from "crypto-js";
import { message } from "antd";
import { datadogLogs } from "@datadog/browser-logs";

import { ReactComponent as MicIcon } from "../assets/icons/audioOn.svg";
import { ReactComponent as MicDisabledIcon } from "../assets/icons/audioOff.svg";
import { ReactComponent as CameraIcon } from "../assets/icons/VideoOn.svg";
import { ReactComponent as CameraDisabledIcon } from "../assets/icons/VideoOff.svg";
import { ReactComponent as ScreenShareIcon } from "../assets/icons/Share.svg";
import { ReactComponent as ScreenShareStopIcon } from "../assets/icons/ScreenShareOff.svg";
import { constants } from "./constants";
// import { ReactComponent as CameraDisabledIcon } from "../assets/icons/camera.svg";

export function getSourceIcon(source, enabled) {
  switch (source) {
    case Track.Source.Microphone:
      return enabled ? <MicIcon /> : <MicDisabledIcon />;
    case Track.Source.Camera:
      return enabled ? <CameraIcon /> : <CameraDisabledIcon />;
    case Track.Source.ScreenShare:
      return enabled ? <ScreenShareStopIcon /> : <ScreenShareIcon />;
    default:
      return undefined;
  }
}

export const getLocalStorageToken = () => {
  const token = localStorage.getItem(`${constants.NAME_KEY}:token`);
  if (token) {
    const bytes = CryptoJS.AES.decrypt(token, `${constants.NAME_KEY}-token`);
    return bytes.toString(CryptoJS.enc.Utf8);
  }
  return false;
};

export const setLocalStorage = (keyName, formData) => {
  let stringData = JSON.stringify(formData);
  localStorage.setItem(
    `${constants.NAME_KEY}:${keyName}`,
    CryptoJS.AES.encrypt(
      stringData,
      `${constants.NAME_KEY}-${keyName}`
    ).toString()
  );
};

export const getLocalStorage = (keyName) => {
  const cipherText = localStorage.getItem(`${constants.NAME_KEY}:${keyName}`);
  if (cipherText) {
    const bytes = CryptoJS.AES.decrypt(
      cipherText,
      `${constants.NAME_KEY}-${keyName}`
    );
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  }
  return false;
};

export const useMessageNotification = () => {
  const [messageApi, contextHolder] = message.useMessage();

  const notify = (type, content) => {
    switch (type) {
      case "success":
        messageApi.open({
          type: "success",
          content,
        });
        break;
      case "error":
        messageApi.open({
          type: "error",
          content,
        });
        break;
      case "warning":
        messageApi.open({
          type: "warning",
          content,
        });
        break;
      default:
        console.log("no type");
    }
  };

  return { notify, contextHolder };
};

export function truncateString(str) {
  const words = str.split(" ");
  if (words.length === 1) {
    return str;
  }
  return `${words[0]}...`;
}

export const generateAvatar = (name) => {
  const first = name[0]?.toUpperCase() || "";
  const words = name.split(" ");
  const last = words[1]?.[0]?.toUpperCase() || "";
  return `${first}${last}`;
};

// Utility functions for managing participant colors
export const getParticipantColor = (participantColors, participantId) => {
  return participantColors.get(participantId);
};

export const getAllParticipantColors = (participantColors) => {
  const colorMap = {};
  participantColors.forEach((color, participantId) => {
    colorMap[participantId] = color;
  });
  return colorMap;
};

export const saveParticipantColorsToStorage = (participantColors, meetingId) => {
  const colorData = getAllParticipantColors(participantColors);
  localStorage.setItem(`participant_colors_${meetingId}`, JSON.stringify(colorData));
};

export const loadParticipantColorsFromStorage = (meetingId) => {
  const stored = localStorage.getItem(`participant_colors_${meetingId}`);
  if (stored) {
    const colorData = JSON.parse(stored);
    const colorMap = new Map();
    Object.entries(colorData).forEach(([participantId, color]) => {
      colorMap.set(participantId, color);
    });
    return colorMap;
  }
  return new Map();
};

export function encoder(code) {
  return window.btoa(code);
}

export function decoder(str) {
  // return window.atob(code);
  if (str === "" || str.trim() === "") {
    return false;
  }
  try {
    return window.atob(str);
  } catch (err) {
    return false;
  }
}

export function isSingleParticipantInBreakoutRooms(
  breakoutRooms,
  mainRoomParticipants
) {
  let totalParticipants = 0;

  const roomsArray = Object.values(breakoutRooms);

  for (const room of roomsArray) {
    totalParticipants += room.participants.length;
  }
  const mainRoomParticipantsCount = mainRoomParticipants.length;
  totalParticipants = Math.max(totalParticipants, mainRoomParticipantsCount);
  return totalParticipants === 1;
}

export const generateUUID = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = Math.floor(Math.random() * 16);
    const v = c === "x" ? r : (r % 4) + 8;
    return v.toString(16);
  });
};

export const baseUrlGenerator = (link) => {
  return `${window.location.protocol}//${window.location.hostname}${
    window.location.port ? `:${window.location.port}` : ""
  }${link || ""}`;
};

export const onDeviceError = (error) => {
  console.log("Error in track toggle", error);
  datadogLogs.logger.error("Error in track toggle", {
    error,
  });
};


export const getMediaPermissions = async () => {
  try {
    const permissions = await window.electronAPI.requestMediaPermissions();

    if (!permissions) {
      console.warn("Media permissions not granted.");
      // Handle lack of permissions (e.g., notify user)
    }
  } catch (error) {
    console.error(
      "Unexpected error while requesting media permissions:",
      error
    );
    // Handle unexpected errors
  }
};

export const parseMetadata = (metadata) => {
  if (!metadata || typeof metadata !== 'string' || metadata.trim() === '') {
    return {};
  }

  try {
    const parsed = JSON.parse(metadata);
    return typeof parsed === 'object' ? parsed : {};
  } catch (error) {
    console.error('Error parsing metadata:', error);
    return {};
  }
};