/* eslint-disable */
import { tokenize, createDefaultGrammar } from "@livekit/components-core";
import { Avatar, Modal, Popover } from "antd";
import * as React from "react";
import { useState } from "react";
import { Document, Page } from "react-pdf";
import { MdFileDownload } from "react-icons/md";
import { MdOutlineZoomIn, MdOutlineZoomOut } from "react-icons/md";
import { MdZoomInMap } from "react-icons/md";
import { generateAvatar, getParticipantColor } from "../../utils/helper";
import { ReactComponent as ReplyIco } from "./Assets/replyIco.svg";
import { ReactComponent as ImageIcon } from "./Assets/imageIcon.svg";
import { ReactComponent as PDFIcon } from "./Assets/pdfIcon.svg";
// import { DownScaleImage } from "./DownScaleImage";

/** @public */
const ChatsEntry = React.forwardRef(
  (
    {
      entry,
      hideName = false,
      hideTimestamp = false,
      messageFormatter,
      uploadedfile,
      uploadedFileName,
      showprivatechatdrawer,
      setReplyMessage,
      replyMessage,
      chatMessages,
      scrollToRepliedChat,
      participantColors = new Map(),
      ...props
    },
    ref
  ) => {
    // const formattedMessage = React.useMemo(() => {
    //   return messageFormatter ? messageFormatter(entry.message) : entry.message;
    // }, [entry.message, messageFormatter]);
    
    const hasBeenEdited = !!entry.editTimestamp;
    const time = new Date(entry.timestamp);
    const locale = navigator ? navigator.language : "en-US";

    // Helper function to get participant color with fallback
    const getAvatarColor = (participantIdentity) => {
      if (participantColors && participantIdentity) {
        const color = getParticipantColor(participantColors, participantIdentity);
        return color || "#f56a00"; // fallback to original color
      }
      return "#f56a00"; // fallback to original color
    };

    // const [showTimestamp, setShowTimestamp] = useState(false);
    const [showAttatchmentPreview, setShowAttatchmentPreview] = useState(false);
    // const [lastParticipantMessage, setLastParticipantMessage] = useState(false);
    
    // const showTime = () => {
    //   setShowTimestamp(!showTimestamp);
    // };

    const handlePreview = () => {
      setShowAttatchmentPreview(true);
    }

    const handleDownload=()=>{
      window.open(entry.message.split(" ")[0], '_blank');
    }

    const formattedMessage = React.useMemo(() => {
      return messageFormatter ? messageFormatter(entry.message, handlePreview, uploadedFileName) : entry.message;
    }, [entry.message, messageFormatter]);

    const handleReplyToMessage = (name,message,id) => {
      setReplyMessage({name,message,id});
    }

    // // check if the last message was sent by the same participant
    // React.useEffect(() => {
    //   if (chatMessages.length > 0) {
    //     const lastParticipantMessage = chatMessages[chatMessages.length - 1]?.from?.name;
    //     const secondLastParticipantMessage = chatMessages[chatMessages.length - 2]?.from?.name;
    //     if (lastParticipantMessage === secondLastParticipantMessage) {
    //       setLastParticipantMessage(true);
    //     } else {
    //       setLastParticipantMessage(false);
    //     }
    //   }
    // }, [chatMessages]);

  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 }); // Position for panning
  const [isDragging, setIsDragging] = useState(false); // Track drag state
  const [startPos, setStartPos] = useState({ x: 0, y: 0 }); // Drag start position
    
  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3)); // Max zoom level: 3x
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1)); // Min zoom level: 1x (original size)
  const resetZoom = () => setScale(1); // Reset zoom to default

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    setPosition({
      x: e.clientX - startPos.x,
      y: e.clientY - startPos.y,
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };
    // console.log("replyMsg", replyMsg);
    // console.log("entry", entry);

    return (
      <li
        ref={ref}
        className={`lk-chat-entry ${entry?.from?.isLocal ? "lk-local" : ""} ${entry?.isTranscription ? "lk-transcription" : ""}`}
        title={time.toLocaleTimeString(locale, { timeStyle: "full" })}
        data-lk-message-origin={entry.from?.isLocal ? "local" : "remote"}
        {...props}
      >
        {showAttatchmentPreview && (
          <Modal
            open={showAttatchmentPreview}
            onCancel={()=>{
              setShowAttatchmentPreview(false)
              setPosition({ x: 0, y: 0 });
              setScale(1);
            }}
            // title={(
            //   <div className="attatchment-preview-modal-title">
            //     <strong>Attachment Preview</strong>
            //     {props.canDownloadChatAttachment && (
            //       <DownloadIco onClick={handleDownload} className="attatchment-download"/>
            //     )}
            //   </div>
            // )}
            footer={null}
            className="lk-attatchment-preview"
            maskStyle={{ 
              background: "rgba(0, 0, 0, 0.7)", 
              backdropFilter: "blur(4px)",
              // zIndex: "1030"
            }}
          >
            {props.canDownloadChatAttachment && (
              <MdFileDownload
                onClick={handleDownload}
                className="attatchment-download"
              />
            )}
            <span className="attatchment-name">
              {entry.message.split("-file-").pop()}
            </span>
            {/pdf/.test(entry.message) ? (
              <iframe
                type="application/pdf"
                title={entry.message}
                // src={`https://docs.google.com/gview?url=${entry.message}&embedded=true`}
                src={entry.message.split(" ")[0]}
                className="lk-chat-pdf-preview"
                style={{width: "100%", height: "500px"}}
              />
            ) : null}
            {/jpeg|jpg|png|gif/.test(entry.message) && (
              <div
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                style={{
                  cursor: isDragging ? 'grabbing' : 'grab',
                  background: "transparent",
                }}
              >
                <img
                  src={entry.message.split(" ")[0]}
                  alt="Attachment Preview"
                  style={{ 
                    width: "100%" ,
                    transform: `scale(${scale})`,
                    transition: 'transform 0.2s ease-in-out',
                    transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                    transition: isDragging ? 'none' : 'transform 0.2s ease',
                    userSelect: "none",
                  }}
                  onDoubleClick={()=>{
                    zoomIn();
                  }}
                />
                <MdOutlineZoomIn 
                  onClick={()=>
                    zoomIn()
                  }
                  className="attatchment-zoom-in"
                />
                <MdOutlineZoomOut 
                  onClick={()=>
                    zoomOut()
                  }
                  className="attatchment-zoom-out"
                />
                <MdZoomInMap 
                  onClick={()=>
                    resetZoom()
                  }
                  className="attatchment-zoom-reset"
                />
              </div>
            )}
          </Modal>
        )}
        {(!hideTimestamp || !hideName || hasBeenEdited) && (
          <span className="lk-meta-data">
            <div className="lk-meta-data-name">
              {entry.from?.isLocal || showprivatechatdrawer ? null : (
                <Avatar style={{ backgroundColor: getAvatarColor(entry.from?.identity) }} size="medium">
                  {generateAvatar(
                    // Use "Agent" for avatar generation if it's an agent with empty name
                    (!entry.from?.name && (
                      entry.from?.identity?.startsWith('agent-') ||
                      entry.from?._attributes?.['lk.agent.state']
                    )) ? "Agent" : entry.from?.name
                  )}
                </Avatar>
              )}
              {!hideName && (
                <strong className="lk-participant-name">
                  {entry.from?.isLocal ? "You" : (
                    // Check if it's an agent participant with empty name
                    (!entry.from?.name && (
                      entry.from?.identity?.startsWith('agent-') ||
                      entry.from?._attributes?.['lk.agent.state']
                    )) ? "Agent" : entry.from?.name
                  )}
                </strong>
              )}
             
            </div>
          </span>
        )}
        <div className="lk-message-outer">
          <Popover
            content={(
              <ReplyIco 
                className="lk-message-body-reply-icon"
                onClick={(e) => {
                  e.stopPropagation();
                  const displayName = (!entry.from?.name && (
                    entry.from?.identity?.startsWith('agent-') ||
                    entry.from?._attributes?.['lk.agent.state']
                  )) ? "Agent" : entry.from?.name;
                  handleReplyToMessage(displayName,entry.message,entry.id);
                }}
              />
            )}
            // trigger="click"
            overlayClassName="lk-message-body-reply"
            placement={`${entry?.from?.isLocal ? "left" : "right"}`}
          >
            <span 
              className={`lk-message-body ${
                (/jpeg|jpg|png|gif/.test(entry.message)) ?
                  'lk-message-body-image' : ''}
                ${(/pdf/.test(entry.message)) ? 'lk-message-body-pdf' : ''}
                ${entry.replyMessage ? 'lk-message-body-replied-message' : ''}
                ${entry.isTranscription ? 'lk-transcription-message' : ''}`
              } 
              onClick={() => {
                if (entry.replyMessage) {
                  scrollToRepliedChat(entry.replyMessage.id);
                }
                // console.log("entry", entry);
              }}
              // onDoubleClick={()=>handleReplyToMessage(entry.from?.name,entry.message)}
              // style={/pdf/.test(entry.message) ? { overflow: 'hidden' } : {}}
              >
              <div className={`lk-chat-body-outer ${/pdf/.test(entry.message) ? 'lk-chat-pdf' : ''}`}>
              {entry.isReplied && (
                <div className="lk-chat-body-inner">
                  <div className="lk-chat-body-inner-avtar">
                    <Avatar style={{ backgroundColor: getAvatarColor(entry.replyMessage?.participantIdentity) }} size={20}>
                      {generateAvatar(entry.replyMessage?.name || "Agent")}
                    </Avatar>
                    <span>{entry.replyMessage?.name || "Agent"}</span>
                  </div>
                  {entry.isReplied && (
                    /jpeg|png|gif|jpg/.test(entry.replyMessage.message) ? (
                      <div className="lk-chat-reply-image">
                        <div className="lk-chat-reply-image-box">
                          <ImageIcon /> {entry?.replyMessage?.message.split(" ").slice(1).join(" ").substring(0, 20) + (entry?.replyMessage?.message.length > 20 ? "..." : "")}
                          {/* <div>Image</div> */}
                        </div>
                      </div>
                    ) : /pdf/.test(entry.replyMessage.message) ? (
                      <div className="lk-chat-reply-image">
                        <div className="lk-chat-reply-image-box">
                          <PDFIcon /> {entry?.replyMessage?.message.split(" ").slice(1).join(" ").substring(0, 20) + (entry?.replyMessage?.message.length > 20 ? "..." : "")}
                        </div>
                      </div>
                    ) : (
                      <div className="lk-chat-reply">
                        {/* <div>
                          <Avatar style={{ backgroundColor: "#f56a00" }} size={20}>
                            {generateAvatar(entry.replyMessage?.name)}
                          </Avatar>
                          <span>{entry.replyMessage?.name}</span>
                        </div> */}
                        <span className="lk-chat-reply-message">
                          {entry.replyMessage?.message.length > 50 ? `${entry.replyMessage?.message.substring(0, 50)}...` : entry.replyMessage?.message}
                        </span>
                      </div>
                    )
                  )}
                  {/* {/pdf/.test(entry.message) ? (
                    <>
                      {entry.isReplied && (
                        <Document
                          file={entry.replyMessage.message.split(" ")[0]}
                          loading=""
                          className="lk-chat-pdf-preview"
                        >
                          <Page
                            pageNumber={1}
                            width={180}
                            className={"lk-chat-pdf-page"}
                            renderAnnotationLayer={false}
                            renderTextLayer={false}
                          />
                        </Document>
                      )}
                    </>
                  ) : /jpeg|jpg|png|gif/.test(entry.message) ? (
                    <>
                    </>
                  ) : (
                    null
                  )} */}
                </div>
              )}
                {formattedMessage}
              </div>
            </span>
          </Popover>
        </div>
      </li>
    );
  }
);

export { ChatsEntry };

/** @public */
export function formatChatMessageLinks(message, handlePreview) {
  return tokenize(message, createDefaultGrammar()).map((tok, i) => {
    if (typeof tok === "string") {
      return tok;
    } else {
      const content = tok.content.toString();
      // console.log("content",message.split(" ")[1]);
      const href =
        tok.type === "url"
          ? /^http(s?):\/\//.test(content)
            ? content
            : `https://${content}`
          : `mailto:${content}`;

         // Check if it's an image or PDF
      if (tok.type === 'url' && /\.(jpg|jpeg|png|gif)$/i.test(content)) {
        // Extract the text after the image link from the message
        // const remainingMessage = message.split(content)[1]?.trim() || '';

        // Return an image preview with dynamic text below
        return (
          <div key={i} className="lk-chat-media-img">
            <img 
              src={`${href}?w=200px&h=200px`} 
              alt="Attatchment Preview" 
              className="lk-chat-image-preview" 
              onClick={(e) => {
                e.stopPropagation();
                handlePreview(); // Trigger modal here
              }}
            />
            {/* <DownScaleImage
              href={href}
              handlePreview={handlePreview}
            /> */}
            {/* <p className="lk-chat-text-below">{remainingMessage}</p> */}
          </div>
        );
      } 
      // else if(tok.type === 'url' && !/\.(jpg|jpeg|png|gif|pdf)$/i.test(content)) {
      //   return (
      //     <a
      //       className="lk-chat-link"
      //       key={i}
      //       href={href}
      //       target="_blank"
      //       rel="noreferrer"
      //     >
      //       {content}
      //     </a>
      //   );
      // }
      else if (tok.type === 'url' && /\.pdf$/i.test(content)) {
        // Extract the text after the PDF link
        // const remainingMessage = message.split(content)[1]?.trim() || '';
        const fileName=message.split("-file-").pop();
        // Return a PDF preview with dynamic text below
        return (
          <div key={i} className="lk-chat-media">
            {/* <iframe
              type="application/pdf"
              title={content}
              src={`https://docs.google.com/gview?url=${href}&embedded=true`}
              className="lk-chat-pdf-preview"
              style={{width: "12.2rem", height: "auto"}}
            /> */}
            <div className="lk-chat-media-pdf">
              <Document 
                file={href}
                className="lk-chat-pdf-preview"
                loading=""
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreview(); // Trigger modal here
                }}
                >
                <Page 
                  pageNumber={1}
                  width={180}
                  className={"lk-chat-pdf-page"}
                  renderAnnotationLayer={false}
                  renderTextLayer={false}
                />
              </Document>
              <div className="lk-chat-media-overlay">
                <span className="lk-chat-pdf-title">
                  {fileName.length > 15
                    ? `${fileName.split('.').slice(0, -1).join('.').substring(0, 12)}...${fileName.split('.').pop()}`
                    : fileName
                  }
                </span>
                <MdFileDownload onClick={(e) => {
                  e.stopPropagation();
                  window.open(href, '_blank');
                }} className="lk-chat-pdf-download"/>
              </div>
            </div>
            {/* <p className={`${message.split(" ")[1] ? "lk-chat-media-message-show" : "lk-chat-media-message-hide"}`}>
              {message.split(" ")[1]}
            </p> */}
          </div>
        );
      }

      return (
        <a
          className="lk-chat-link"
          key={i}
          href={href}
          target="_blank"
          rel="noreferrer"
        >
          {content}
        </a>
      );
    }
  });
}
