import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { Avatar, Badge, Progress} from "antd";
// import { useMaybeLayoutContext } from "@livekit/components-react";
import { CloseOutlined } from "@ant-design/icons";
import { ChatsEntry } from "./ChatsEntry";
import { cloneSingleChild } from "../../utils/cloneSingleChild";
import { constants, DataReceivedEvent } from "../../utils/constants";
import { Endpoints } from "../../API/Endpoints/routes";
import { generateAvatar, generateUUID, getParticipantColor } from "../../utils/helper";
import { ReactComponent as SendBtnIcon } from "../../assets/icons/send_btn.svg";
import "./PrivateChat.scss"; // Import the SCSS file
import { ReactComponent as InfoIco } from "./Assets/Info.svg";
import { ReactComponent as ImageIcon } from "./Assets/imageIcon.svg";
import UploadIcon from "./Assets/clipIcon.png";
import { ReactComponent as PDFIcon } from "./Assets/pdfIcon.svg";
import { ReactComponent as DeleteIcon } from "./Assets/deleteIcon.svg";

export default function PrivateChats({
  messageFormatter,
  messageDecoder,
  messageEncoder,
  privatechatparticipants,
  setprivatechatparticipants,
  selectedprivatechatparticipant,
  setselectedprivatechatparticipant,
  localparticipant,
  privatechatmessages,
  setprivatechatmessages,
  newmessagerender,
  showprivatechatdrawer,
  setPrivateChatUnreadMessagesCount,
  privateChatUnreadMessagesCount,
  showchatdrawer,
  setToastNotification,
  setToastStatus,
  setShowToast,
  participantColors = new Map(),
  ...props
}) {
  const inputRef = useRef(null);
  const ulRef = useRef(null);

  const [chatMessages, setChatMessages] = useState([]);
  const [uploadedfile, setUploadedFile] = useState(null);
  const [replyMessage, setReplyMessage] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState("");
  const [uploadedFileName, setUploadedFileName] = useState(null);
  const locale = navigator ? navigator.language : "en-US";

  // Helper function to get participant color with fallback
  const getAvatarColor = (participantIdentity) => {
    if (participantColors && participantIdentity) {
      const color = getParticipantColor(participantColors, participantIdentity);
      return color || "#f56a00"; // fallback to original color
    }
    return "#f56a00"; // fallback to original color
  };

  useEffect(() => {
    if (uploadedfile) {
      setUploadedFileName(uploadedfile.name);
    }
  }, [uploadedfile]);

  useEffect(() => {
    if (selectedprivatechatparticipant !== null) {
      setChatMessages(
        privatechatmessages.get(
          selectedprivatechatparticipant.participant.identity
        ) || []
      );
    }
  }, [selectedprivatechatparticipant, newmessagerender]);

  useEffect(() => {
    // Sort participants: connected (true) first, disconnected (false) last
    const sortedParticipants = [...privatechatparticipants].sort(
      (a, b) => b.isConnected - a.isConnected
    );

    // Update state with sorted participants
    setprivatechatparticipants(sortedParticipants);
  }, []);

  // const layoutContext = useMaybeLayoutContext();
  // const lastReadMsgAt = useRef(0);

  async function handleSubmit(event) {
    event.preventDefault();
    let thumbnailUrl = null;
    if (
      (inputRef.current && inputRef.current.value.trim() !== "") ||
      uploadedfile !== null
    ) {
      if (uploadedfile !== null) {
        const formData = new FormData();
        formData.append("file", uploadedfile);
        const response = await axios.post(
          `${constants.STAG_BASE_URL}${Endpoints.send_chat_attachment.url}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              const progressPercent = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              setUploadProgress(progressPercent);
              setUploadStatus("uploading");
              setUploadedFile(uploadedfile);
            },
          }
        );
        if (response.data.success === 1) {
          thumbnailUrl = response.data.data.url;
          const uploaded = true;
          setUploadStatus("uploaded");
          setUploadProgress(0);
          setUploadedFile(!uploaded);
        } else {
          setToastNotification("Error in uploading file");
          setToastStatus("error");
          setShowToast(true);
          // console.error("Error uploading file", response.data);
        }
      }

      const encoder = new TextEncoder();
      const message =
        thumbnailUrl !== null
          ? inputRef.current && inputRef.current.value.trim() !== ""
            ? `${thumbnailUrl} ${inputRef.current.value}`
            : thumbnailUrl
          : inputRef.current.value;
      const msgId = generateUUID();
      const encodedMessage = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.SEND_PRIVATE_MESSAGE,
          id: msgId,
          message,
          timestamp: Date.now(),
          isReplied: replyMessage !== null,
          replyMessage,
        })
      );
      localparticipant.publishData(encodedMessage, {
        reliable: true,
        destinationIdentities: [
          selectedprivatechatparticipant.participant.identity,
        ],
      });

      setUploadedFile(null);
      thumbnailUrl = null;
      inputRef.current.value = "";
      inputRef.current.style.height = "40px";
      inputRef.current.focus();
      chatMessages.push({
        id: msgId,
        from: localparticipant,
        message,
        timestamp: Date.now(),
        isReplied: replyMessage !== null,
        replyMessage,
      });
      setChatMessages([...chatMessages]);
      setprivatechatmessages(
        privatechatmessages.set(
          selectedprivatechatparticipant.participant.identity,
          chatMessages
        )
      );
      setReplyMessage(null);
    }
  }

  useEffect(() => {
    if (ulRef.current) {
      ulRef.current.scrollTo({ top: ulRef.current.scrollHeight });
    }
  }, [chatMessages]);

  const hasUpdatedUnreadCountRef = useRef(0);

  useEffect(() => {
    if (
      selectedprivatechatparticipant !== null &&
      privateChatUnreadMessagesCount > 0 &&
      showprivatechatdrawer &&
      showchatdrawer &&
      hasUpdatedUnreadCountRef.current <
        chatMessages[chatMessages?.length - 1]?.timestamp // Check if the effect has already run
    ) {
      setPrivateChatUnreadMessagesCount(
        (prev) =>
          prev - selectedprivatechatparticipant.receivedUnreadMessagesCount
      );
      setselectedprivatechatparticipant((prev) => ({
        ...prev,
        receivedUnreadMessagesCount: 0,
      }));
      setprivatechatparticipants((prev) =>
        prev.map((participant) =>
          participant.key === selectedprivatechatparticipant.key
            ? { ...participant, receivedUnreadMessagesCount: 0 }
            : participant
        )
      );

      // Mark as executed
      hasUpdatedUnreadCountRef.current =
        chatMessages[chatMessages?.length - 1]?.timestamp;
    }
  }, [
    selectedprivatechatparticipant,
    privateChatUnreadMessagesCount,
    showprivatechatdrawer,
    showchatdrawer,
    chatMessages,
  ]);

  const handleChannelChange = (participant) => {
    setselectedprivatechatparticipant(participant);
  };

  const handleUpload = () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*,application/pdf";

    fileInput.onchange = (event) => {
      const file = event.target.files[0];

      if (file) {
        // const fileURL = URL.createObjectURL(file); // Create a URL for the file
        // console.log("File URL:", fileURL);
        setUploadedFile(file);

        // Optionally, you can clean up the URL after use
        // URL.revokeObjectURL(fileURL);
      } else {
        setToastNotification("No file selected.");
        setShowToast(true);
        setToastStatus("info");
        // console.log("No file selected");
      }
    };

    fileInput.click(); // Programmatically click the hidden input to open the file dialog
  };

  const groupedMessages = chatMessages.reduce((acc, msg) => {
    const lastGroup = acc[acc.length - 1];
    if (lastGroup && lastGroup.from === msg.from) {
      lastGroup.messages.push(msg);
    } else {
      acc.push({ from: msg.from, messages: [msg] });
    }
    return acc;
  }, []);

  return (
    <div className="private-chats-container">
      {privatechatparticipants?.length > 0 ? (
        <>
          <div className="private-chats-scrollable">
            {privatechatparticipants.map((participant) => (
              <div
                className="private-chat-participant"
                key={participant.key}
                style={{ cursor: "pointer" }}
                onClick={() => handleChannelChange(participant)}
              >
                {selectedprivatechatparticipant?.key === participant?.key ? (
                  <Avatar
                    style={{
                      backgroundColor: getAvatarColor(participant?.participant?.identity),
                      border: "3px solid #9747FF",
                      display: "flex",
                      alignItems: "center",
                    }}
                    size="medium"
                  >
                    {generateAvatar(participant?.participant?.name)}
                  </Avatar>
                ) : (
                  <Badge
                    count={participant.receivedUnreadMessagesCount}
                    overflowCount={9}
                    color="blue"
                  >
                    <Avatar
                      size="medium"
                      style={{ backgroundColor: getAvatarColor(participant?.participant?.identity) }}
                    >
                      {generateAvatar(participant?.participant?.name)}
                    </Avatar>
                  </Badge>
                )}
                {selectedprivatechatparticipant?.key === participant?.key ? (
                  <span className="lk-participant-name-selected">
                    {participant?.participant?.name.split(" ")[0]}
                  </span>
                ) : (
                  <span className="lk-participant-name">
                    {participant?.participant?.name.split(" ")[0]}
                  </span>
                )}
              </div>
            ))}
          </div>
          <div className="private-chats-container-info">
            <span>
              <InfoIco /> You are in{" "}
              {`${
                selectedprivatechatparticipant?.participant?.name.split(" ")[0]
              }'s`}{" "}
              private chat window
            </span>
          </div>
          <div className="lk-chat">
            <ul
              className={`lk-list lk-chat-messages lk-chat-messages-private
                ${replyMessage && "reply-essage-in-progress"}
                ${uploadedfile && "lk-chat-file-uploaded"}`}
              ref={ulRef}
            >
              {props.children
                ? chatMessages.map((msg, idx) =>
                    cloneSingleChild(props.children, {
                      entry: msg,
                      key: msg.id ?? idx,
                      messageFormatter,
                    })
                  )
                : groupedMessages.map((group, groupIdx) => (
                    <div key={groupIdx} className="sender-group">
                      {group.messages.map((msg, idx, allMsg) => {
                        const hideTimestamp =
                          idx >= 1 &&
                          msg.timestamp - allMsg[idx - 1].timestamp < 60_000;
                        return (
                          <>
                            <ChatsEntry
                              key={msg.id ?? idx}
                              hideName={idx > 0} // Hide name for subsequent messages in the group
                              hideTimestamp={hideTimestamp}
                              entry={msg}
                              messageFormatter={messageFormatter}
                              uploadedfile={uploadedfile}
                              uploadedFileName={uploadedFileName}
                              showprivatechatdrawer={showprivatechatdrawer}
                              canDownloadChatAttachment={
                                props.canDownloadChatAttachment
                              }
                              setReplyMessage={setReplyMessage}
                              replyMessage={replyMessage}
                              chatMessages={chatMessages}
                              participantColors={participantColors}
                            />
                          </>
                        );
                      })}
                      <span
                        style={{ fontSize: "10px" }}
                        className={`${
                          group.from === localparticipant
                            ? "lk-chat-sender"
                            : "lk-chat-receiver"
                        }`}
                      >
                        {(() => {
                          const lastMessage =
                            group.messages[group.messages.length - 1];
                          const time = new Date(lastMessage.timestamp);
                          return time.toLocaleTimeString(locale, {
                            timeStyle: "short",
                          });
                        })()}
                      </span>
                    </div>

                    // : chatMessages.map((msg, idx, allMsg) => {
                    //     const hideName =
                    //       idx >= 1 && allMsg[idx - 1].from === msg.from;
                    //     const hideTimestamp =
                    //       idx >= 1 &&
                    //       msg.timestamp - allMsg[idx - 1].timestamp < 60_000;

                    //     return (
                    //       <ChatsEntry
                    //         key={msg.id ?? idx}
                    //         hideName={hideName}
                    //         hideTimestamp={
                    //           hideName === false ? false : hideTimestamp
                    //         }
                    //         entry={msg}
                    //         messageFormatter={messageFormatter}
                    //         uploadedfile={uploadedfile}
                    //         showprivatechatdrawer={showprivatechatdrawer}
                    //         setReplyMessage={setReplyMessage}
                    //       />
                    //     );
                    //   }
                  ))}
            </ul>
            {selectedprivatechatparticipant.isConnected && (
              <form className="input-area lk-chat-form" onSubmit={handleSubmit}>
                <div className="lk-chat-form-outer">
                  {replyMessage && (
                    <div className="reply-message">
                      <div className="reply-message-text">
                        <div className="reply-message-sender">
                          <Avatar
                            style={{
                              backgroundColor: getAvatarColor(replyMessage.participantIdentity),
                              marginRight: "0.4rem",
                            }}
                            size={16}
                          >
                            {generateAvatar(replyMessage.name)}
                          </Avatar>
                          <span>{replyMessage.name}</span>
                        </div>
                        <div className="reply-message-text-msg">
                          <span>
                            {replyMessage?.message?.length > 20
                              ? `${replyMessage.message.substring(0, 20)}...`
                              : replyMessage.message}
                          </span>
                        </div>
                      </div>
                      <div
                        className="reply-message-close"
                        onClick={() => setReplyMessage(null)}
                      >
                        <CloseOutlined />
                      </div>
                    </div>
                  )}
                  <textarea
                    className="text-area lk-form-control lk-chat-form-input "
                    // disabled={isSending}
                    ref={inputRef}
                    type="text"
                    placeholder="Type a message..."
                    // onInput={(ev) => ev.stopPropagation()}
                    onInput={(ev) => {
                      ev.stopPropagation();
                      ev.target.style.height = `${ev.target.scrollHeight}px`;

                      // Reset to minimum height if the textarea is cleared
                      if (!ev.target.value) {
                        ev.target.style.height = "40px"; // Set to your desired minimum height
                      }
                    }}
                    onKeyDown={(ev) => {
                      if (ev.key === "Enter" && !ev.shiftKey) {
                        ev.stopPropagation();
                        ev.preventDefault(); // Prevents a new line from being added
                        handleSubmit(ev);
                      }
                    }}
                    onKeyUp={(ev) => ev.stopPropagation()}
                  />
                  {uploadedfile && (
                    <div
                      className={`file-upload ${
                        uploadProgress === 100 && "file-upload-success"
                      }`}
                    >
                      <span className="file-upload-first">
                        {uploadedfile?.type.includes(
                          "jpeg",
                          "jpg",
                          "png",
                          "gif"
                        ) ? (
                          <div className="file-upload-image">
                            <ImageIcon />
                          </div>
                        ) : null}
                        {uploadedfile?.type.includes("pdf") ? (
                          <div className="file-upload-image">
                            <PDFIcon
                              style={{ width: "20px", height: "20px" }}
                            />
                          </div>
                        ) : null}
                        <div className="file-upload-right">
                          <span>
                            {uploadedfile?.name.length > 15
                              ? `${uploadedfile.name
                                  .split(".")
                                  .slice(0, -1)
                                  .join(".")
                                  .substring(0, 12)}...${uploadedfile.name
                                  .split(".")
                                  .pop()}`
                              : uploadedfile.name}
                          </span>
                          <div className="file-uplaod-right-subhead">
                            <span>
                              {uploadedfile.size > 1024 * 1024
                                ? `${(
                                    uploadedfile.size /
                                    (1024 * 1024)
                                  ).toFixed(2)} MB`
                                : `${(uploadedfile.size / 1024).toFixed(2)} KB`}
                            </span>
                            <span>
                              {uploadProgress === 100 ? (
                                "Upload Successful!"
                              ) : uploadProgress === 0 ? (
                                "Upload"
                              ) : uploadStatus === "uploading" ? (
                                "Uploading..."
                              ) : (
                                <div style={{ color: "#E14045" }}>
                                  Upload Failed
                                </div>
                              )}
                            </span>
                          </div>
                          <Progress
                            className="file-upload-progress"
                            percent={uploadProgress}
                            status={
                              uploadProgress === 100 ? "success" : "active"
                            }
                            // showInfo={false}
                          />
                        </div>
                      </span>
                      <div className="file-upload-end">
                        <DeleteIcon
                          onClick={() => {
                            setUploadProgress(0);
                            setUploadStatus("");
                            setUploadedFile(null);
                            // handleCancelUpload();
                          }}
                        />
                      </div>
                    </div>
                  )}
                  <div className="lk-chat-form-buttons">
                    {/* <div className="lk-chat-form-emoji">
                    <Emojis />
                    EMOJIS
                  </div> */}
                    <div
                      className="lk-chat-form-upload"
                      style={{ color: "white" }}
                      onClick={() => handleUpload()}
                    >
                      <img src={UploadIcon} alt="" className="lk-chat-form-upload-icon" />
                    </div>
                    <div
                      onClick={(e) => {
                        handleSubmit(e);
                      }}
                      className="send-btn lk-button lk-chat-form-button"
                    >
                      <SendBtnIcon />
                    </div>
                  </div>
                </div>
                {/* </button> */}
              </form>
            )}
          </div>
        </>
      ) : (
        <div className="no-participants">
          No Private message from participants
        </div>
      )}
    </div>
  );
}
