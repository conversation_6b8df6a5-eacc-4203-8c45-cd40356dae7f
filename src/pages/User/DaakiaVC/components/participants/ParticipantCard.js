/* eslint-disable no-unused-vars */
import * as React from "react";
import { useState, useEffect, useRef } from "react";
import { Avatar } from "antd";
// import { stringToColor } from "../../utils/helper";
// import "./ParticipantCard.scss";
import "./styles/ParticipantCard.scss";
import { ParticipantsPopOver } from "./ParticipantsPopOver";
import { ReactComponent as MicrophoneCloseIcon } from "./icons/MicrophoneCloseIcon.svg";
import { ReactComponent as MicrophoneOpenIcon } from "./icons/MicrophoneOpenIcon.svg";
import { ReactComponent as VideoOff } from "./icons/VideoOff.svg";
import { ReactComponent as VideoOn } from "./icons/VideoOn.svg";
import { truncateString, generateAvatar, parseMetadata, getParticipantColor } from "../../utils/helper";
import { ParticipantService } from "../../services/ParticipantServices";

function UserInfo({ name, time, isSelf,isAgent, avatarName, icons, onNameUpdate,isHost,isCoHost, participantColors = new Map(), participantIdentity }) {
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState(name);
  const inputRef = useRef(null); // Create a ref for the input element

  const handleEditClick = () => {
    setIsEditing(true);
  };

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.select(); // Select the text in the input field when entering edit mode
      inputRef.current.focus(); // Focus the input field when entering edit mode
    }
  }, [isEditing]);

  const handleInputChange = (e) => {
    setNewName(e.target.value);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      onNameUpdate(newName); // Call API or parent function to update the name
      setIsEditing(false);
    }
  };

  // Helper function to get participant color with fallback
  const getAvatarColor = (participantIdentity) => {
    if (participantColors && participantIdentity) {
      const color = getParticipantColor(participantColors, participantIdentity);
      return color || "#fd4563"; // fallback to original color
    }
    return "#fd4563"; // fallback to original color
  };

  return (
    <div title={name} className="user-info">
      <Avatar style={{ backgroundColor: getAvatarColor(participantIdentity), verticalAlign: "middle" }}>
        {isAgent ? "A":avatarName}
      </Avatar>
      <div className="user-details">
        <div className="username">
          {isEditing ? (
            <input
              ref={inputRef} // Attach the ref to the input field
              type="text"
              value={newName}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
              className="username-input"
            />
          ) : (
            <>
              {isAgent ? "Agent" : name.length > 20 ? `${name.substring(0, 20)}...` : name}
              {isSelf ? " (You)" : ""}
            </>
          )}
          {!isEditing && (isSelf || isHost || isCoHost) && (
            <span
              className="edit-icon"
              onClick={handleEditClick}
              style={{ cursor: "pointer", marginLeft: "10px" }}
            >
              ✎
            </span>
          )}
        </div>
        <div className="ptc-icos">
          <div className="timestamp">{time}</div>
          {/* ActionIcons is assumed to be another component */}
          <ActionIcons icons={icons} isSelf={isSelf} />
        </div>
      </div>
      <style>
        {`
          .user-details .edit-icon {
            visibility: hidden;
          }
          .user-details:hover .edit-icon {
            visibility: visible;
          }
        `}
      </style>
    </div>
  );
}
function ActionIcons({ icons, isSelf }) {
  return (
    <div className={`action-icons ${isSelf ? "action-icons-admin" : ""}`}>
      {icons.map((icon, index) => (
        <div key={index} className="icon">
          {icon}
        </div>
      ))}
    </div>
  );
}

const getMinutesDifference = (date1, date2) => {
  const diffInMs = Math.abs(date2 - date1);
  return Math.floor(diffInMs / (1000 * 60));
};

export function ParticipantCard({
  participant,
  localParticipant,
  raisedHand,
  id,
  isHost,
  layoutContext,
  isCoHost,
  coHostToken,
  meetingFeatures,
  setselectedprivatechatparticipant,
  setprivatechatparticipants,
  privatechatparticipants,
  setDrawerState,
  setshowprivatechat,
  forcemute,
  forcevideooff,
  room,
  isBreakoutRoom,
  breakoutRooms,
  allowLiveCollabWhiteBoard,
  isPinned,
  setIsPinned,
  setToastNotification,
  setToastStatus,
  setShowToast,
  participantColors = new Map(),
}) {
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // const isSelf = participant.isLocal ? " (You)" : "";
  const userInfo = {
    truncatedName: truncateString(participant.name),
    name: participant.name, // Assuming isSelf is a boolean indicating the current user
    time: `${getMinutesDifference(
      new Date(),
      new Date(participant?.joinedAt)
    )} mins ${
      (() => {
        const metadata = parseMetadata(participant.metadata);
        return metadata?.role_name === "moderator"
          ? "(Host)"
          : metadata?.role_name === "cohost"
          ? "(Co-Host)"
          : "";
      })()
    }`,
    isSelf: participant?.isLocal,
    isAgent: participant?.isAgent,
    avatarName: generateAvatar(participant?.name),
    isHost,
    isCoHost,
    participantColors,
    participantIdentity: participant?.identity
  };

  const actionIcons = [];

  if (participant.isCameraEnabled) {
    actionIcons.push(<VideoOn />);
  } else {
    actionIcons.push(<VideoOff />);
  }

  if (participant.isMicrophoneEnabled) {
    actionIcons.push(<MicrophoneOpenIcon />);
  } else {
    actionIcons.push(<MicrophoneCloseIcon className="pt-mic-off-ico" />);
  }

  if (!participant.isLocal && !participant?.isAgent) {
    actionIcons.push(
      <ParticipantsPopOver
        participant={participant}
        localParticipant={localParticipant}
        meetingUid={id}
        isHost={isHost}
        layoutContext={layoutContext}
        isCoHost={isCoHost}
        coHostToken={coHostToken}
        meetingFeatures={meetingFeatures}
        setselectedprivatechatparticipant={setselectedprivatechatparticipant}
        setprivatechatparticipants={setprivatechatparticipants}
        privatechatparticipants={privatechatparticipants}
        setDrawerState={setDrawerState}
        setshowprivatechat={setshowprivatechat}
        forcemute={forcemute}
        forcevideooff={forcevideooff}
        room={room}
        isBreakoutRoom={isBreakoutRoom}
        breakoutRooms={breakoutRooms}
        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
        isPinned={isPinned}
        setIsPinned={setIsPinned}
        setToastNotification={setToastNotification}
        setToastStatus={setToastStatus}
        setShowToast={setShowToast}
      />
    );
  }

  const onNameUpdate = async (newName) => {
    try {
      await ParticipantService.updateParticipantName(
        id,
        participant,
        newName,
        coHostToken,
        localParticipant?.participantInfo
      );
      userInfo.avatarName = generateAvatar(newName);
      setToastNotification("Name Updated successfully!");
      setToastStatus("success");
      setShowToast(true);
    } catch (error) {
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.error(error);
    }
  };

  return (
    <article className="post-card">
      <UserInfo {...userInfo} icons={actionIcons} onNameUpdate={onNameUpdate} />
      {/* <ActionIcons icons={actionIcons} {...userInfo}/> */}
    </article>
  );
}
