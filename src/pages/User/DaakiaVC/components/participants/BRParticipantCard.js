import React from "react";
import { Avatar } from "antd";
import { generateAvatar, parseMetadata, getParticipantColor } from "../../utils/helper";
// import "./BreakoutRoom.scss";
import "./styles/BreakoutRoom.scss";

export function BRParticipantCard({ participant, participantColors = new Map() }) {
  const metadata = parseMetadata(participant.metadata);
  const role = `${
    metadata?.role_name === "moderator"
      ? "(Host)"
      : metadata?.role_name === "cohost"
      ? "(Co-Host)"
      : ""
  }`;

  // Helper function to get participant color with fallback
  const getAvatarColor = (participantIdentity) => {
    if (participantColors && participantIdentity) {
      const color = getParticipantColor(participantColors, participantIdentity);
      return color || "#fd4563"; // fallback to original color
    }
    return "#fd4563"; // fallback to original color
  };

  return (
    <div title={participant.name} className="br-user-info">
      <Avatar
        style={{
          backgroundColor: getAvatarColor(participant.identity),
          verticalAlign: "middle",
        }}
      >
        {generateAvatar(participant.name)}
      </Avatar>
      <div className="user-details">
        <div className="username">
          {participant.name}
          {/* {participant.isLocal ? " (You)" : ""} */}
        </div>
        <div className="timestamp">{role}</div>
      </div>
    </div>
  );
}
